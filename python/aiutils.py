# 导入模块
import requests
import json
import requests
import warnings
import os
import socket
os.environ['OPENAI_API_KEY'] = 'sk-zk26720dccc28d5150ad37fee3d886afdef65d741242b193'
import openai


computer_name = socket.gethostname()
print(computer_name)

if computer_name == 'tian':
    os.environ["http_proxy"] = "http://127.0.0.1:7890"
    os.environ["https_proxy"] = "http://127.0.0.1:7890"
elif computer_name == 'zhaodsh-pc':
    os.environ["http_proxy"] = "http://127.0.0.1:7890"
    os.environ["https_proxy"] = "http://127.0.0.1:7890"
else:
    pass


def llm_agi(model, prompt):
    client = openai.OpenAI(
        api_key='sk-zk26720dccc28d5150ad37fee3d886afdef65d741242b193',
        base_url="https://api.zhizengzeng.com/v1",
    )
    print('使用模型：' + model)

    response = client.chat.completions.create(
        messages=[
            {
                "role": "user",
                "content": prompt,
            }
        ],
        model=model,
        temperature=0.75  # 调整温度，控制生成文本的创造性
    )

    # 提取生成的总结概要
    summary = response.choices[0].message.content
    return summary


