#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quote/0 API 图形界面客户端
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import requests
import base64
import json
import threading
from pathlib import Path
import os
from aiutils import llm_agi


class Quote0GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Quote/0 消息发送器 & 单词管理")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 配置参数
        self.api_key = "dot_app_hBpJUdQwxjGqdoiTFsYTPEondtDsSGKECsHQJDlfIbefVVYajmxuJLlQiyxsVHiS"
        self.device_id = "E4B063CC5A74"
        self.icon_path = None

        # 单词管理相关
        self.words_list = []  # 存储单词列表 [{"word": "hello", "meaning": "你好"}, ...]
        self.words_file = "words.json"  # 单词文件名
        self.selected_word_index = None  # 当前选中的单词索引

        self.setup_ui()
        self.load_words()  # 启动时加载单词

    def setup_ui(self):
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # 创建消息发送选项卡
        self.message_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(self.message_frame, text="消息发送")

        # 创建单词管理选项卡
        self.words_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(self.words_frame, text="单词管理")

        # 设置选项卡框架的网格权重
        self.message_frame.columnconfigure(1, weight=1)
        self.words_frame.columnconfigure(1, weight=1)

        # 设置界面
        self.setup_message_ui()
        self.setup_words_ui()

    def setup_message_ui(self):
        """设置消息发送界面"""
        # 标题
        title_label = ttk.Label(self.message_frame, text="Quote/0 消息发送器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 标题输入框 - 使用Text控件增加高度
        ttk.Label(self.message_frame, text="标题:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=5)
        self.title_text = tk.Text(self.message_frame, height=2, width=50, wrap=tk.WORD)
        self.title_text.insert(tk.END, "")
        self.title_text.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)

        # 标题滚动条
        title_scrollbar = ttk.Scrollbar(self.message_frame, orient=tk.VERTICAL, command=self.title_text.yview)
        title_scrollbar.grid(row=3, column=2, sticky=(tk.N, tk.S), pady=5)
        self.title_text.configure(yscrollcommand=title_scrollbar.set)

        # 消息内容
        ttk.Label(self.message_frame, text="消息内容:").grid(row=4, column=0, sticky=(tk.W, tk.N), pady=5)
        self.message_text = tk.Text(self.message_frame, height=6, width=50, wrap=tk.WORD)
        self.message_text.insert(tk.END, "")
        self.message_text.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)

        # 消息滚动条
        scrollbar = ttk.Scrollbar(self.message_frame, orient=tk.VERTICAL, command=self.message_text.yview)
        scrollbar.grid(row=4, column=2, sticky=(tk.N, tk.S), pady=5)
        self.message_text.configure(yscrollcommand=scrollbar.set)

        # 发送按钮
        self.send_button = ttk.Button(self.message_frame, text="发送消息", command=self.send_message_thread)
        self.send_button.grid(row=8, column=0, columnspan=2, pady=20)

        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(self.message_frame, textvariable=self.status_var)
        status_label.grid(row=9, column=0, columnspan=2, pady=5)

        # 日志显示
        ttk.Label(self.message_frame, text="日志:").grid(row=10, column=0, sticky=(tk.W, tk.N), pady=5)
        self.log_text = tk.Text(self.message_frame, height=8, width=50, wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.grid(row=10, column=1, sticky=(tk.W, tk.E), pady=5)

        log_scrollbar = ttk.Scrollbar(self.message_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=10, column=2, sticky=(tk.N, tk.S), pady=5)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

    def setup_words_ui(self):
        """设置单词管理界面"""
        # 标题
        title_label = ttk.Label(self.words_frame, text="单词管理", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 单词输入区域
        input_frame = ttk.LabelFrame(self.words_frame, text="添加/编辑单词", padding="10")
        input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)

        # 英文单词输入
        ttk.Label(input_frame, text="英文单词:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.word_entry = ttk.Entry(input_frame, width=30)
        self.word_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 中文释义输入
        ttk.Label(input_frame, text="中文释义:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.meaning_entry = ttk.Entry(input_frame, width=30)
        self.meaning_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 操作按钮
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)

        self.add_button = ttk.Button(button_frame, text="添加单词", command=self.add_word)
        self.add_button.grid(row=0, column=0, padx=5)

        self.update_button = ttk.Button(button_frame, text="更新单词", command=self.update_word, state=tk.DISABLED)
        self.update_button.grid(row=0, column=1, padx=5)

        self.delete_button = ttk.Button(button_frame, text="删除单词", command=self.delete_word, state=tk.DISABLED)
        self.delete_button.grid(row=0, column=2, padx=5)

        self.clear_button = ttk.Button(button_frame, text="清空输入", command=self.clear_inputs)
        self.clear_button.grid(row=0, column=3, padx=5)

        # 单词列表显示
        list_frame = ttk.LabelFrame(self.words_frame, text="单词列表", padding="10")
        list_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 创建Treeview显示单词列表
        columns = ("word", "meaning")
        self.words_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=12)
        self.words_tree.heading("word", text="英文单词")
        self.words_tree.heading("meaning", text="中文释义")
        self.words_tree.column("word", width=200)
        self.words_tree.column("meaning", width=300)
        self.words_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 添加滚动条
        words_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.words_tree.yview)
        words_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.words_tree.configure(yscrollcommand=words_scrollbar.set)

        # 绑定选择事件
        self.words_tree.bind("<<TreeviewSelect>>", self.on_word_select)

        # 文件操作按钮
        file_frame = ttk.Frame(self.words_frame)
        file_frame.grid(row=3, column=0, columnspan=3, pady=10)

        self.save_button = ttk.Button(file_frame, text="保存到文件", command=self.save_words)
        self.save_button.grid(row=0, column=0, padx=5)

        self.load_button = ttk.Button(file_frame, text="从文件加载", command=self.load_words)
        self.load_button.grid(row=0, column=1, padx=5)

        # 状态显示
        self.words_status_var = tk.StringVar(value="单词管理就绪")
        words_status_label = ttk.Label(self.words_frame, textvariable=self.words_status_var)
        words_status_label.grid(row=4, column=0, columnspan=3, pady=5)

        # 配置网格权重
        self.words_frame.rowconfigure(2, weight=1)

    def select_icon(self):
        """选择图标文件"""
        file_path = filedialog.askopenfilename(
            title="选择图标文件",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.gif *.bmp"), ("所有文件", "*.*")]
        )
        if file_path:
            self.icon_path = file_path
            self.icon_label.config(text=Path(file_path).name)

    def clear_icon(self):
        """清除图标"""
        self.icon_path = None
        self.icon_label.config(text="未选择")

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def encode_image_to_base64(self, image_path):
        """将图片文件编码为base64格式"""
        try:
            with open(image_path, 'rb') as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            self.log_message(f"图标文件读取失败: {e}")
            return None

    def send_message_thread(self):
        """在新线程中发送消息"""

        def send():
            self.send_button.config(state=tk.DISABLED)
            self.status_var.set("发送中...")

            try:
                self.send_message()
            finally:
                self.send_button.config(state=tk.NORMAL)
                self.status_var.set("就绪")

        threading.Thread(target=send, daemon=True).start()

    def send_message(self):
        """发送消息到设备"""
        # 获取输入值 - 修复变量引用
        api_key = self.api_key
        device_id = self.device_id
        title = self.title_text.get("1.0", tk.END).strip()
        message = self.message_text.get("1.0", tk.END).strip()

        # 验证必填字段
        if not all([api_key, device_id, title, message]):
            messagebox.showerror("错误", "请填写所有必填字段（标题、消息内容）")
            return

        # API地址
        url = "https://dot.mindreset.tech/api/open/text"

        # 请求头
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Quote0-Python-Client/1.0'
        }

        # 构建请求数据
        payload = {
            "deviceId": device_id,
            "title": title,
            "message": message
        }

        # 添加签名（使用默认值）
        payload["signature"] = "zhaodsh"

        if self.icon_path:
            icon_base64 = self.encode_image_to_base64(self.icon_path)
            if icon_base64:
                payload["icon"] = icon_base64
                self.log_message(f"✓ 成功加载图标: {Path(self.icon_path).name}")

        # 显示发送信息
        self.log_message("正在发送消息...")
        self.log_message(f"设备ID: {device_id}")
        self.log_message(f"标题: {title}")
        self.log_message(f"消息: {message}")
        self.log_message(f"签名: zhaodsh")
        self.log_message("-" * 40)

        try:
            # 发送请求
            response = requests.post(url, headers=headers, json=payload, timeout=30, verify=False,
                                     proxies={"http": None, "https": None})

            # 显示结果
            self.log_message(f"状态码: {response.status_code}")

            if response.ok:
                self.log_message("✓ 消息发送成功！")
                messagebox.showinfo("成功", "消息发送成功！")
            else:
                self.log_message("✗ 消息发送失败！")
                messagebox.showerror("失败", "消息发送失败！")

            # 显示响应内容
            try:
                response_data = response.json()
                self.log_message("响应内容:")
                self.log_message(json.dumps(response_data, indent=2, ensure_ascii=False))
            except:
                self.log_message("响应内容:")
                self.log_message(response.text)

        except requests.exceptions.Timeout:
            error_msg = "请求超时，请检查网络连接"
            self.log_message(f"✗ {error_msg}")
            messagebox.showerror("错误", error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = "连接失败，请检查网络或API地址"
            self.log_message(f"✗ {error_msg}")
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            error_msg = f"发送失败: {e}"
            self.log_message(f"✗ {error_msg}")
            messagebox.showerror("错误", error_msg)

    # 单词管理功能方法
    def add_word(self):
        """添加新单词"""
        word = self.word_entry.get().strip()
        meaning = self.meaning_entry.get().strip()

        if not word or not meaning:
            messagebox.showerror("错误", "请输入英文单词和中文释义")
            return

        # 检查单词是否已存在
        for existing_word in self.words_list:
            if existing_word["word"].lower() == word.lower():
                messagebox.showerror("错误", f"单词 '{word}' 已存在")
                return

        # 添加单词到列表
        new_word = {"word": word, "meaning": meaning}
        self.words_list.append(new_word)

        # 更新显示
        self.refresh_words_display()
        self.clear_inputs()
        self.words_status_var.set(f"已添加单词: {word}")

    def update_word(self):
        """更新选中的单词"""
        if self.selected_word_index is None:
            messagebox.showerror("错误", "请先选择要更新的单词")
            return

        word = self.word_entry.get().strip()
        meaning = self.meaning_entry.get().strip()

        if not word or not meaning:
            messagebox.showerror("错误", "请输入英文单词和中文释义")
            return

        # 检查是否与其他单词重复（除了当前选中的单词）
        for i, existing_word in enumerate(self.words_list):
            if i != self.selected_word_index and existing_word["word"].lower() == word.lower():
                messagebox.showerror("错误", f"单词 '{word}' 已存在")
                return

        # 更新单词
        old_word = self.words_list[self.selected_word_index]["word"]
        self.words_list[self.selected_word_index] = {"word": word, "meaning": meaning}

        # 更新显示
        self.refresh_words_display()
        self.clear_inputs()
        self.words_status_var.set(f"已更新单词: {old_word} -> {word}")

    def delete_word(self):
        """删除选中的单词"""
        if self.selected_word_index is None:
            messagebox.showerror("错误", "请先选择要删除的单词")
            return

        word_to_delete = self.words_list[self.selected_word_index]["word"]

        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除单词 '{word_to_delete}' 吗？"):
            del self.words_list[self.selected_word_index]
            self.refresh_words_display()
            self.clear_inputs()
            self.words_status_var.set(f"已删除单词: {word_to_delete}")

    def clear_inputs(self):
        """清空输入框"""
        self.word_entry.delete(0, tk.END)
        self.meaning_entry.delete(0, tk.END)
        self.selected_word_index = None
        self.update_button.config(state=tk.DISABLED)
        self.delete_button.config(state=tk.DISABLED)

    def on_word_select(self, event):
        """处理单词选择事件"""
        selection = self.words_tree.selection()
        if selection:
            item = self.words_tree.item(selection[0])
            values = item['values']
            if values:
                # 填充输入框
                self.word_entry.delete(0, tk.END)
                self.word_entry.insert(0, values[0])
                self.meaning_entry.delete(0, tk.END)
                self.meaning_entry.insert(0, values[1])

                # 找到对应的索引
                for i, word_data in enumerate(self.words_list):
                    if word_data["word"] == values[0] and word_data["meaning"] == values[1]:
                        self.selected_word_index = i
                        break

                # 启用更新和删除按钮
                self.update_button.config(state=tk.NORMAL)
                self.delete_button.config(state=tk.NORMAL)

    def refresh_words_display(self):
        """刷新单词列表显示"""
        # 清空现有显示
        for item in self.words_tree.get_children():
            self.words_tree.delete(item)

        # 添加所有单词
        for word_data in self.words_list:
            self.words_tree.insert("", tk.END, values=(word_data["word"], word_data["meaning"]))

    def save_words(self):
        """保存单词列表到文件"""
        try:
            with open(self.words_file, 'w', encoding='utf-8') as f:
                json.dump(self.words_list, f, ensure_ascii=False, indent=2)
            self.words_status_var.set(f"已保存 {len(self.words_list)} 个单词到 {self.words_file}")
            messagebox.showinfo("成功", f"单词列表已保存到 {self.words_file}")
        except Exception as e:
            error_msg = f"保存失败: {e}"
            self.words_status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)

    def load_words(self):
        """从文件加载单词列表"""
        try:
            if os.path.exists(self.words_file):
                with open(self.words_file, 'r', encoding='utf-8') as f:
                    self.words_list = json.load(f)
                self.refresh_words_display()
                self.clear_inputs()
                self.words_status_var.set(f"已加载 {len(self.words_list)} 个单词从 {self.words_file}")
            else:
                self.words_status_var.set(f"文件 {self.words_file} 不存在")
        except Exception as e:
            error_msg = f"加载失败: {e}"
            self.words_status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)


def main():
    root = tk.Tk()
    app = Quote0GUI(root)
    root.mainloop()




def word_prompt(word):
    prompt = f'''
        你是一个英语例句生成器。给定一个目标单词，生成恰好10个展示该单词用法的例句。
        
        要求：
        1. 每个句子必须包含目标单词
        2. 除目标单词外，其他所有词汇必须是高中水平（适合中国高考水平）
        3. 每个句子不超过12个单词
        4. 如果适用，句子应展示该单词的不同含义/用法
        5. 使用简单、清晰的语法结构
        6. 确保句子自然且有意义
        
        输入格式：一个英语单词
        
        输出格式：仅返回一个有效的JSON对象，严格按照以下结构：
        ====
        {{
          "word": "目标单词",
          "sentences": [
            "句子1",
            "句子2", 
            "句子3",
            "句子4",
            "句子5",
            "句子6",
            "句子7",
            "句子8",
            "句子9",
            "句子10"
          ]
        }}
        =====
        不要包含任何解释、注释或额外文本。仅返回JSON对象。
        
        目标单词：{word}
    '''


if __name__ == "__main__":

    ret = llm_agi("gpt-5", "debate")
    print(ret)
    # main()