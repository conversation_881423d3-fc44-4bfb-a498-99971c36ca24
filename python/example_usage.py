#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例脚本：展示如何在其他 Python 脚本中使用 llm_agi 函数

这个脚本展示了几种不同的导入和使用方式
"""

# 方法1：直接从 aiutils 模块导入
from aiutils import llm_agi

# 方法2：从新创建的 llm_utils 模块导入
from llm_utils import call_llm

# 方法3：导入整个模块
import aiutils

def example_usage_1():
    """使用方法1：直接导入的函数"""
    print("=== 方法1：直接从 aiutils 导入 ===")
    
    model = "deepseek-v3-241226"  # 实际上这个参数会被函数内部覆盖
    prompt = "请用中文简单介绍一下人工智能的发展历史"
    
    try:
        response = llm_agi(model, prompt)
        print(f"提问：{prompt}")
        print(f"回答：{response}")
        print("-" * 50)
    except Exception as e:
        print(f"调用失败：{e}")

def example_usage_2():
    """使用方法2：使用封装的便捷函数"""
    print("=== 方法2：使用 llm_utils 中的便捷函数 ===")
    
    model = "any-model"  # 这个参数实际不会被使用
    prompt = "请解释一下什么是机器学习"
    
    try:
        response = call_llm(model, prompt)
        print(f"提问：{prompt}")
        print(f"回答：{response}")
        print("-" * 50)
    except Exception as e:
        print(f"调用失败：{e}")

def example_usage_3():
    """使用方法3：通过模块调用"""
    print("=== 方法3：通过模块调用 ===")
    
    model = "deepseek-v3-241226"
    prompt = "请用一句话总结深度学习的核心思想"
    
    try:
        response = aiutils.llm_agi(model, prompt)
        print(f"提问：{prompt}")
        print(f"回答：{response}")
        print("-" * 50)
    except Exception as e:
        print(f"调用失败：{e}")

def batch_questions_example():
    """批量提问示例"""
    print("=== 批量提问示例 ===")
    
    questions = [
        "什么是Python？",
        "如何学习编程？",
        "人工智能的应用领域有哪些？"
    ]
    
    for i, question in enumerate(questions, 1):
        try:
            print(f"\n问题 {i}：{question}")
            answer = llm_agi("deepseek-v3-241226", question)
            print(f"回答：{answer}")
        except Exception as e:
            print(f"问题 {i} 调用失败：{e}")

if __name__ == "__main__":
    print("LLM_AGI 函数使用示例")
    print("=" * 60)
    
    # 运行各种使用示例
    example_usage_1()
    example_usage_2()
    example_usage_3()
    batch_questions_example()
    
    print("\n示例运行完成！")
    print("\n使用说明：")
    print("1. 确保你的网络可以访问 API 服务")
    print("2. 函数会自动使用 deepseek-v3-241226 模型")
    print("3. 如果你在特定的计算机上运行，代理设置会自动配置")
    print("4. 可以根据需要修改 aiutils.py 中的 API 密钥和基础 URL")
