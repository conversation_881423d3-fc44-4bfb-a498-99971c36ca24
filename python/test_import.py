#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证 llm_agi 函数的各种导入方式是否正常工作
"""

def test_import_methods():
    """测试不同的导入方式"""
    
    print("测试 llm_agi 函数的导入方式...")
    print("=" * 50)
    
    # 测试方法1：直接从 aiutils 导入
    try:
        from aiutils import llm_agi
        print("✓ 方法1成功：from aiutils import llm_agi")
    except ImportError as e:
        print(f"✗ 方法1失败：{e}")
    
    # 测试方法2：从 llm_utils 导入
    try:
        from llm_utils import call_llm, llm_agi
        print("✓ 方法2成功：from llm_utils import call_llm, llm_agi")
    except ImportError as e:
        print(f"✗ 方法2失败：{e}")
    
    # 测试方法3：导入整个模块
    try:
        import aiutils
        print("✓ 方法3成功：import aiutils")
        # 验证函数是否存在
        if hasattr(aiutils, 'llm_agi'):
            print("  - aiutils.llm_agi 函数存在")
        else:
            print("  - aiutils.llm_agi 函数不存在")
    except ImportError as e:
        print(f"✗ 方法3失败：{e}")
    
    print("\n" + "=" * 50)
    print("导入测试完成！")

def test_function_call():
    """测试函数调用（不实际发送请求）"""
    print("\n测试函数调用...")
    print("=" * 50)
    
    try:
        from aiutils import llm_agi
        
        # 检查函数是否可调用
        if callable(llm_agi):
            print("✓ llm_agi 函数可调用")
            
            # 显示函数信息
            print(f"函数名称: {llm_agi.__name__}")
            if hasattr(llm_agi, '__doc__') and llm_agi.__doc__:
                print(f"函数文档: {llm_agi.__doc__.strip()}")
            
            print("\n注意：实际调用需要网络连接和有效的 API 密钥")
            print("如果要测试实际调用，请运行 example_usage.py")
        else:
            print("✗ llm_agi 不是一个可调用的函数")
            
    except Exception as e:
        print(f"✗ 函数调用测试失败：{e}")

if __name__ == "__main__":
    test_import_methods()
    test_function_call()
    
    print("\n" + "=" * 60)
    print("使用建议：")
    print("1. 推荐使用：from aiutils import llm_agi")
    print("2. 便捷方式：from llm_utils import call_llm")
    print("3. 模块方式：import aiutils; aiutils.llm_agi(...)")
    print("4. 运行 example_usage.py 查看完整使用示例")
    print("5. 查看 LLM_USAGE_GUIDE.md 获取详细文档")
