# LLM_AGI 函数使用指南

本指南说明如何在其他 Python 脚本中使用 `llm_agi` 函数。

## 函数说明

`llm_agi` 函数位于 `aiutils.py` 文件中，用于调用 DeepSeek API 进行文本生成。

### 函数签名
```python
def llm_agi(model, prompt):
    """
    调用 LLM API 生成文本
    
    Args:
        model (str): 模型名称（会被自动覆盖为 'deepseek-v3-241226'）
        prompt (str): 输入的提示文本
    
    Returns:
        str: LLM 生成的回复文本
    """
```

## 使用方法

### 方法1：直接导入（推荐）

```python
from aiutils import llm_agi

# 使用函数
response = llm_agi("deepseek-v3-241226", "你好，请介绍一下自己")
print(response)
```

### 方法2：使用便捷工具模块

```python
from llm_utils import call_llm

# 使用封装的便捷函数
response = call_llm("any-model", "请解释什么是人工智能")
print(response)
```

### 方法3：模块导入

```python
import aiutils

# 通过模块调用
response = aiutils.llm_agi("deepseek-v3-241226", "请写一首关于春天的诗")
print(response)
```

### 方法4：从包导入（如果使用 __init__.py）

```python
from python import llm_agi

# 直接使用
response = llm_agi("deepseek-v3-241226", "请总结一下今天的天气")
print(response)
```

## 完整示例

### 简单使用示例

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from aiutils import llm_agi

def ask_question(question):
    """向 LLM 提问并获取回答"""
    try:
        answer = llm_agi("deepseek-v3-241226", question)
        return answer
    except Exception as e:
        return f"调用失败：{e}"

# 使用示例
if __name__ == "__main__":
    questions = [
        "什么是机器学习？",
        "Python 有哪些优势？",
        "如何学习编程？"
    ]
    
    for question in questions:
        print(f"问题：{question}")
        answer = ask_question(question)
        print(f"回答：{answer}")
        print("-" * 50)
```

### 批量处理示例

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from aiutils import llm_agi
import time

def batch_process_questions(questions, delay=1):
    """批量处理问题列表"""
    results = []
    
    for i, question in enumerate(questions, 1):
        print(f"处理问题 {i}/{len(questions)}: {question}")
        
        try:
            answer = llm_agi("deepseek-v3-241226", question)
            results.append({
                'question': question,
                'answer': answer,
                'status': 'success'
            })
        except Exception as e:
            results.append({
                'question': question,
                'answer': None,
                'status': 'error',
                'error': str(e)
            })
        
        # 添加延迟避免请求过于频繁
        if i < len(questions):
            time.sleep(delay)
    
    return results

# 使用示例
if __name__ == "__main__":
    questions = [
        "请解释什么是深度学习",
        "Python 和 Java 的区别是什么？",
        "如何提高编程技能？"
    ]
    
    results = batch_process_questions(questions)
    
    # 输出结果
    for result in results:
        print(f"\n问题：{result['question']}")
        if result['status'] == 'success':
            print(f"回答：{result['answer']}")
        else:
            print(f"错误：{result['error']}")
```

## 注意事项

1. **网络连接**：确保你的网络可以访问 API 服务地址
2. **代理设置**：函数会根据计算机名自动设置代理（针对特定机器）
3. **API 密钥**：当前使用的是硬编码的 API 密钥，生产环境建议使用环境变量
4. **模型固定**：函数内部会将模型固定为 'deepseek-v3-241226'
5. **错误处理**：建议在调用时添加 try-catch 错误处理

## 环境要求

确保安装了以下依赖：
```bash
pip install openai requests
```

## 自定义配置

如果需要修改 API 配置，可以编辑 `aiutils.py` 文件中的以下部分：

```python
# API 密钥
api_key = 'your-api-key-here'

# API 基础 URL
base_url = "https://api.zhizengzeng.com/v1"

# 模型名称
model = 'deepseek-v3-241226'

# 温度参数
temperature = 0.75
```

## 运行示例

要运行提供的示例脚本：

```bash
cd python
python example_usage.py
```

这将展示各种使用方法的示例。
